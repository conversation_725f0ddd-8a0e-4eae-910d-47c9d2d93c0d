<?php

if (!defined('ABSPATH')) { exit; }

/*

<PERSON><PERSON><PERSON><PERSON>m Sayfası - Tutor Entegrasyonu
* @version 1.0.0

Bu template dosyası düzenlenebilir ve kendi özel template'iniz ile değiştirilebilir. Bunun için bu dosyayı tema (veya child tema) klasörünüz altında 'marketking' adlı bir klasöre kopyalayın ve orada düzenleyin.

Örneğin, temanız storefront ise, bu dosyayı wp-content/themes/storefront/marketking/ altına kopyalayabilir ve kendi özel içeriğinizle düzenleyebilirsiniz.

*/

// Tutor eklentisinin yüklü olup olmadığını kontrol et
if (!function_exists('tutor') || !current_user_can(tutor()->instructor_role)) {
    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="alert alert-warning">
                        <?php esc_html_e('Bu sayfaya erişim için Tutor eklentisinin yüklü olması ve eğitmen yetkisine sahip olmanız gerekmektedir.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

use TUTOR\Input;
use Tutor\Models\CourseModel;

// Kullanıcı ID'sini al
$current_user_id = get_current_user_id();
if (marketking()->is_vendor_team_member()) {
    $current_user_id = marketking()->get_team_member_parent();
}

// Aktif sekmeyi belirle
$active_tab = Input::get('tab', 'my-courses');

// Kurs durumlarını eşleştir
$status_map = array(
    'my-courses' => CourseModel::STATUS_PUBLISH,
    'draft-courses' => CourseModel::STATUS_DRAFT,
    'pending-courses' => CourseModel::STATUS_PENDING,
    'schedule-courses' => CourseModel::STATUS_FUTURE,
);

// Mevcut sekme için kurs durumunu ayarla
$status = isset($status_map[$active_tab]) ? $status_map[$active_tab] : CourseModel::STATUS_PUBLISH;
$post_type = apply_filters('tutor_dashboard_course_list_post_type', array(tutor()->course_post_type));

// Kurs sekmelerinin sayılarını al
$count_map = array(
    'publish' => CourseModel::get_courses_by_instructor($current_user_id, CourseModel::STATUS_PUBLISH, 0, 0, true, $post_type),
    'pending' => CourseModel::get_courses_by_instructor($current_user_id, CourseModel::STATUS_PENDING, 0, 0, true, $post_type),
    'draft' => CourseModel::get_courses_by_instructor($current_user_id, CourseModel::STATUS_DRAFT, 0, 0, true, $post_type),
    'future' => CourseModel::get_courses_by_instructor($current_user_id, CourseModel::STATUS_FUTURE, 0, 0, true, $post_type),
);

$per_page = tutor_utils()->get_option('courses_per_page', 10);
$paged = Input::get('current_page', 1, Input::TYPE_INT);
$offset = $per_page * ($paged - 1);
$results = CourseModel::get_courses_by_instructor($current_user_id, $status, $offset, $per_page, false, $post_type);
$show_course_delete = true;

$tabs = array(
    'publish' => array(
        'title' => __('Yayınlanan', 'marketking-multivendor-marketplace-for-woocommerce'),
        'link' => 'my-courses',
    ),
    'pending' => array(
        'title' => __('Beklemede', 'marketking-multivendor-marketplace-for-woocommerce'),
        'link' => 'pending-courses',
    ),
    'draft' => array(
        'title' => __('Taslak', 'marketking-multivendor-marketplace-for-woocommerce'),
        'link' => 'draft-courses',
    ),
    'future' => array(
        'title' => __('Programlanan', 'marketking-multivendor-marketplace-for-woocommerce'),
        'link' => 'schedule-courses',
    ),
);

if (!current_user_can('administrator') && !tutor_utils()->get_option('instructor_can_delete_course')) {
    $show_course_delete = false;
}
?>

<div class="nk-content marketking_kurslarim_page">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h4 class="nk-block-title page-title"><?php esc_html_e('Kurslarım', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                            <div class="nk-block-des text-soft">
                                <p><?php esc_html_e('Tüm kurslarınızı buradan yönetebilirsiniz.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <?php if (function_exists('tutor_pro')) : ?>
                                <?php do_action('tutor_course_create_button'); ?>
                            <?php else : ?>
                                <a href="#" class="btn btn-primary tutor-create-new-course">
                                    <em class="icon ni ni-plus"></em>
                                    <span><?php esc_html_e('Yeni Kurs', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card">
                        <div class="card-inner">
                            <!-- Sekme Navigasyonu -->
                            <div class="nk-tb-list nk-tb-ulist">
                                <div class="nk-tb-item nk-tb-head">
                                    <ul class="nav nav-tabs nav-tabs-mb-icon nav-tabs-card">
                                        <?php foreach ($tabs as $key => $tab) : ?>
                                            <li class="nav-item">
                                                <a class="nav-link<?php echo esc_attr($tab['link'] === $active_tab ? ' active' : ''); ?>" 
                                                   href="<?php echo esc_url(add_query_arg('tab', $tab['link'], trailingslashit(get_page_link(apply_filters('wpml_object_id', get_option('marketking_vendordash_page_setting', 'disabled'), 'post', true))) . 'kurslarim')); ?>">
                                                    <?php echo esc_html($tab['title']); ?> 
                                                    <span class="badge badge-gray badge-pill"><?php echo esc_html($count_map[$key]); ?></span>
                                                </a>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>

                            <!-- Kurs Listesi -->
                            <div class="nk-tb-list">
                                <?php
                                $placeholder_img = tutor()->url . 'assets/images/placeholder.svg';

                                if (!is_array($results) || (!count($results) && 1 == $paged)) {
                                    ?>
                                    <div class="text-center py-4">
                                        <em class="icon ni ni-book" style="font-size: 3rem; opacity: 0.3;"></em>
                                        <h6 class="text-soft"><?php esc_html_e('Henüz kurs bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h6>
                                        <p class="text-soft"><?php esc_html_e('İlk kursunuzu oluşturmak için yukarıdaki "Yeni Kurs" butonunu kullanın.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                                    </div>
                                    <?php
                                } else {
                                    ?>
                                    <div class="row g-gs">
                                        <?php
                                        global $post;
                                        $tutor_nonce_value = wp_create_nonce(tutor()->nonce_action);
                                        foreach ($results as $post) :
                                            setup_postdata($post);

                                            $avg_rating = tutor_utils()->get_course_rating()->rating_avg;
                                            $tutor_course_img = get_tutor_course_thumbnail_src();
                                            $id_string_delete = 'tutor_my_courses_delete_' . $post->ID;
                                            $row_id = 'tutor-dashboard-my-course-' . $post->ID;
                                            $course_duration = get_tutor_course_duration_context($post->ID, true);
                                            $course_students = tutor_utils()->count_enrolled_users_by_course();
                                            $is_main_instructor = CourseModel::is_main_instructor($post->ID);
                                            $course_edit_link = apply_filters('tutor_dashboard_course_list_edit_link', tutor_utils()->course_edit_link($post->ID, tutor()->has_pro ? 'frontend' : 'backend'), $post);
                                            ?>

                                            <div class="col-sm-6 col-lg-4 col-xxl-3">
                                                <div class="card card-bordered product-card h-100" id="<?php echo esc_attr($row_id); ?>">
                                                    <div class="product-thumb">
                                                        <a href="<?php echo esc_url(get_the_permalink()); ?>">
                                                            <img src="<?php echo empty($tutor_course_img) ? esc_url($placeholder_img) : esc_url($tutor_course_img); ?>" 
                                                                 class="card-img-top" alt="<?php the_title(); ?>" loading="lazy">
                                                        </a>
                                                        <?php if (false === $is_main_instructor) : ?>
                                                            <span class="badge badge-primary badge-pill position-absolute" style="top: 10px; right: 10px;">
                                                                <?php esc_html_e('Ortak Yazar', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="card-inner text-center">
                                                        <ul class="product-tags">
                                                            <li><span class="badge badge-gray"><?php echo esc_html(get_the_date()); ?></span></li>
                                                        </ul>
                                                        <h5 class="product-title">
                                                            <a href="<?php echo esc_url(get_the_permalink()); ?>"><?php the_title(); ?></a>
                                                        </h5>
                                                        
                                                        <?php if (!empty($course_duration) || !empty($course_students)) : ?>
                                                            <div class="product-meta">
                                                                <?php if (!empty($course_duration)) : ?>
                                                                    <span class="text-soft">
                                                                        <em class="icon ni ni-clock"></em>
                                                                        <?php echo wp_kses(stripslashes($course_duration), array('span' => array('class' => true))); ?>
                                                                    </span>
                                                                <?php endif; ?>
                                                                
                                                                <?php if (!empty($course_students)) : ?>
                                                                    <span class="text-soft">
                                                                        <em class="icon ni ni-users"></em>
                                                                        <?php echo wp_kses(stripslashes($course_students), array('span' => array('class' => true))); ?>
                                                                    </span>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php endif; ?>
                                                        
                                                        <div class="product-price text-primary">
                                                            <span class="price">
                                                                <?php
                                                                $price = tutor_utils()->get_course_price();
                                                                if (null === $price) {
                                                                    esc_html_e('Ücretsiz', 'marketking-multivendor-marketplace-for-woocommerce');
                                                                } else {
                                                                    echo wp_kses_post(tutor_utils()->get_course_price());
                                                                }
                                                                ?>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="product-action">
                                                        <ul class="btn-toolbar gx-1">
                                                            <li>
                                                                <a href="<?php echo esc_url($course_edit_link); ?>" class="btn btn-trigger btn-icon" data-toggle="tooltip" data-placement="top" title="<?php esc_attr_e('Düzenle', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                                                                    <em class="icon ni ni-edit"></em>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <div class="dropdown">
                                                                    <a href="#" class="dropdown-toggle btn btn-trigger btn-icon" data-toggle="dropdown">
                                                                        <em class="icon ni ni-more-h"></em>
                                                                    </a>
                                                                    <div class="dropdown-menu dropdown-menu-right">
                                                                        <ul class="link-list-opt no-bdr">
                                                                            <!-- Yayınla/Gönder Aksiyonu -->
                                                                            <?php if (tutor()->has_pro && in_array($post->post_status, array(CourseModel::STATUS_DRAFT), true)) : ?>
                                                                                <?php
                                                                                $params = http_build_query(
                                                                                    array(
                                                                                        'tutor_action' => 'update_course_status',
                                                                                        'status' => CourseModel::STATUS_PENDING,
                                                                                        'course_id' => $post->ID,
                                                                                        tutor()->nonce => $tutor_nonce_value,
                                                                                    )
                                                                                );
                                                                                ?>
                                                                                <li>
                                                                                    <a href="?<?php echo esc_attr($params); ?>">
                                                                                        <em class="icon ni ni-share"></em>
                                                                                        <span>
                                                                                            <?php
                                                                                            $can_publish_course = current_user_can('administrator') || (bool) tutor_utils()->get_option('instructor_can_publish_course');
                                                                                            if ($can_publish_course) {
                                                                                                esc_html_e('Yayınla', 'marketking-multivendor-marketplace-for-woocommerce');
                                                                                            } else {
                                                                                                esc_html_e('Gönder', 'marketking-multivendor-marketplace-for-woocommerce');
                                                                                            }
                                                                                            ?>
                                                                                        </span>
                                                                                    </a>
                                                                                </li>
                                                                            <?php endif; ?>
                                                                            
                                                                            <!-- Kopyala Aksiyonu -->
                                                                            <?php if (tutor()->has_pro && in_array($post->post_status, array(CourseModel::STATUS_PUBLISH, CourseModel::STATUS_PENDING, CourseModel::STATUS_DRAFT))) : ?>
                                                                                <?php
                                                                                $params = http_build_query(
                                                                                    array(
                                                                                        'tutor_action' => 'duplicate_course',
                                                                                        'course_id' => $post->ID,
                                                                                    )
                                                                                );
                                                                                ?>
                                                                                <li>
                                                                                    <a href="?<?php echo esc_attr($params); ?>">
                                                                                        <em class="icon ni ni-copy"></em>
                                                                                        <span><?php esc_html_e('Kopyala', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                                    </a>
                                                                                </li>
                                                                            <?php endif; ?>
                                                                            
                                                                            <!-- Taslağa Taşı Aksiyonu -->
                                                                            <?php if (tutor()->has_pro && in_array($post->post_status, array(CourseModel::STATUS_PUBLISH))) : ?>
                                                                                <?php
                                                                                $params = http_build_query(
                                                                                    array(
                                                                                        'tutor_action' => 'update_course_status',
                                                                                        'status' => CourseModel::STATUS_DRAFT,
                                                                                        'course_id' => $post->ID,
                                                                                        tutor()->nonce => $tutor_nonce_value,
                                                                                    )
                                                                                );
                                                                                ?>
                                                                                <li>
                                                                                    <a href="?<?php echo esc_attr($params); ?>">
                                                                                        <em class="icon ni ni-archive"></em>
                                                                                        <span><?php esc_html_e('Taslağa Taşı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                                    </a>
                                                                                </li>
                                                                            <?php endif; ?>
                                                                            
                                                                            <!-- Gönderimi İptal Et -->
                                                                            <?php if (tutor()->has_pro && in_array($post->post_status, array(CourseModel::STATUS_PENDING))) : ?>
                                                                                <?php
                                                                                $params = http_build_query(
                                                                                    array(
                                                                                        'tutor_action' => 'update_course_status',
                                                                                        'status' => CourseModel::STATUS_DRAFT,
                                                                                        'course_id' => $post->ID,
                                                                                        tutor()->nonce => $tutor_nonce_value,
                                                                                    )
                                                                                );
                                                                                ?>
                                                                                <li>
                                                                                    <a href="?<?php echo esc_attr($params); ?>">
                                                                                        <em class="icon ni ni-cross"></em>
                                                                                        <span><?php esc_html_e('Gönderimi İptal Et', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                                    </a>
                                                                                </li>
                                                                            <?php endif; ?>
                                                                            
                                                                            <!-- Sil Aksiyonu -->
                                                                            <?php if ($is_main_instructor && in_array($post->post_status, array(CourseModel::STATUS_PUBLISH, CourseModel::STATUS_DRAFT))) : ?>
                                                                                <?php if ($show_course_delete) : ?>
                                                                                    <li>
                                                                                        <a href="#" data-toggle="modal" data-target="#<?php echo esc_attr($id_string_delete); ?>" class="text-danger">
                                                                                            <em class="icon ni ni-trash"></em>
                                                                                            <span><?php esc_html_e('Sil', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                                        </a>
                                                                                    </li>
                                                                                <?php endif; ?>
                                                                            <?php endif; ?>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Silme Onay Modalı -->
                                            <div class="modal fade" id="<?php echo esc_attr($id_string_delete); ?>" tabindex="-1">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title"><?php esc_html_e('Kursu Sil?', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h5>
                                                            <a href="#" class="close" data-dismiss="modal" aria-label="Close">
                                                                <em class="icon ni ni-cross"></em>
                                                            </a>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p><?php esc_html_e('Bu kursu kalıcı olarak silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                                                        </div>
                                                        <div class="modal-footer bg-light">
                                                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php esc_html_e('İptal', 'marketking-multivendor-marketplace-for-woocommerce'); ?></button>
                                                            <button type="button" class="btn btn-danger tutor-list-ajax-action" 
                                                                    data-request_data='{"course_id":<?php echo esc_attr($post->ID); ?>,"action":"tutor_delete_dashboard_course","redirect_to":"<?php echo esc_url(tutor_utils()->get_current_url()); ?>"}' 
                                                                    data-delete_element_id="<?php echo esc_attr($row_id); ?>">
                                                                <?php esc_html_e('Evet, Sil', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php
                                        endforeach;
                                        wp_reset_postdata();
                                        ?>
                                    </div>
                                    
                                    <!-- Sayfalama -->
                                    <?php
                                    if ($count_map[$status] > $per_page) {
                                        $pagination_data = array(
                                            'total_items' => $count_map[$status],
                                            'per_page' => $per_page,
                                            'paged' => $paged,
                                        );
                                        
                                        // Basit sayfalama
                                        $total_pages = ceil($count_map[$status] / $per_page);
                                        if ($total_pages > 1) {
                                            ?>
                                            <div class="card-inner">
                                                <div class="nk-block-between-md g-3">
                                                    <div class="g">
                                                        <ul class="pagination justify-content-center justify-content-md-start">
                                                            <?php if ($paged > 1) : ?>
                                                                <li class="page-item">
                                                                    <a class="page-link" href="<?php echo esc_url(add_query_arg('current_page', $paged - 1)); ?>">
                                                                        <?php esc_html_e('Önceki', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                                                    </a>
                                                                </li>
                                                            <?php endif; ?>
                                                            
                                                            <?php for ($i = 1; $i <= $total_pages; $i++) : ?>
                                                                <li class="page-item<?php echo ($i == $paged) ? ' active' : ''; ?>">
                                                                    <a class="page-link" href="<?php echo esc_url(add_query_arg('current_page', $i)); ?>"><?php echo $i; ?></a>
                                                                </li>
                                                            <?php endfor; ?>
                                                            
                                                            <?php if ($paged < $total_pages) : ?>
                                                                <li class="page-item">
                                                                    <a class="page-link" href="<?php echo esc_url(add_query_arg('current_page', $paged + 1)); ?>">
                                                                        <?php esc_html_e('Sonraki', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                                                    </a>
                                                                </li>
                                                            <?php endif; ?>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php
                                        }
                                    }
                                    ?>
                                    <?php
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Tutor nonce'unu JavaScript'e aktar
window.tutorNonce = '<?php echo wp_create_nonce(tutor()->nonce_action); ?>';
</script>
